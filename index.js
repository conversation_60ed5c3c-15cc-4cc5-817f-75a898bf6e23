/**
 * @format
 */

import {AppRegistry} from 'react-native';
import App from './App';
import {name as appName} from './app.json';
import messaging from '@react-native-firebase/messaging';
import notifee from '@notifee/react-native';
import NotificationService from './common/firebase/NotificationService';

messaging().setBackgroundMessageHandler(async remoteMessage => {
  if (!remoteMessage.notification && remoteMessage.data) {
    await NotificationService.displayNotification(remoteMessage);
  }
  NotificationService.refreshNotificationCount();
});

notifee.onBackgroundEvent(async ({type, detail}) => {
  await NotificationService.handleBackgroundEvent({type, detail});
});

AppRegistry.registerComponent(appName, () => App);
