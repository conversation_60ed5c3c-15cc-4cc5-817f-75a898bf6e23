import {
  combineReducers,
  configureStore,
  createListenerMiddleware,
} from '@reduxjs/toolkit';

import AsyncStorage from '@react-native-async-storage/async-storage';
import {TypedUseSelectorHook, useDispatch, useSelector} from 'react-redux';
import {
  createTransform,
  persistReducer,
  persistStore,
  Transform,
} from 'redux-persist';
import reactotron from '../../ReactotronConfig.ts';
import {injectStore} from '../api/api.ts';
import AuthenSlice, {AuthSliceProps} from './reducer/AuthSlice.ts';
import LessonSlice from './reducer/LessonSlice.ts';
import NetInfoSlice, {setConnectionStatus} from './reducer/NetInfoSlice.ts';
import PopUpSlice from './reducer/PopUpSlice.ts';
import ProfileSlice from './reducer/ProfileSlice.ts';
import QuestionSlice from './reducer/QuestionSlice.ts';
import ThemeSlice from './reducer/ThemeSlice.ts';
import UnitSlice from './reducer/UnitSlice.ts';
import SchoolSlice from './reducer/SchoolSlice.ts';
import OfflineSlice, {
  hideOfflineBar,
  showOfflineBar,
} from './reducer/OfflineSlice.ts';
import NotificationService from '../../common/firebase/NotificationService';

const listenerMiddleware = createListenerMiddleware();

let previousConnected: boolean | null = null;

listenerMiddleware.startListening({
  actionCreator: setConnectionStatus,
  effect: async (action, listenerApi) => {
    const nowConnected = action.payload;
    if (previousConnected === null) {
      if (!nowConnected) {
        listenerApi.dispatch(showOfflineBar());
      }
    } else if (previousConnected !== nowConnected) {
      if (!nowConnected) {
        listenerApi.dispatch(showOfflineBar());
      } else {
        listenerApi.dispatch(hideOfflineBar());
      }
    }
    previousConnected = nowConnected;
  },
});

const appReducer = combineReducers({
  auth: AuthenSlice,
  popUp: PopUpSlice,
  unit: UnitSlice,
  school: SchoolSlice,
  lesson: LessonSlice,
  question: QuestionSlice,
  profile: ProfileSlice,
  theme: ThemeSlice,
  netInfo: NetInfoSlice,
  offline: OfflineSlice,
});

const rootReducer = (
  state: ReturnType<typeof appReducer> | undefined,
  action: any,
) => {
  if (action.type === 'RESET_STATE') {
    NotificationService.clearNotificationState();
    const preservedTheme = state?.theme;
    const preservedAuth = {
      remember: state?.auth?.remember,
      firstOpen: state?.auth?.firstOpen,
      deviceToken: state?.auth?.deviceToken,
    };
    state = {
      theme: preservedTheme,
      auth: preservedAuth,
    } as ReturnType<typeof appReducer>;
  }

  return appReducer(state, action);
};

const authTransform: Transform<
  Partial<AuthSliceProps>,
  AuthSliceProps
> = createTransform<Partial<AuthSliceProps>, AuthSliceProps>(
  inboundState => {
    const {token, deviceToken, refreshToken, remember, firstOpen} =
      inboundState;
    return {
      token: token || '',
      deviceToken: deviceToken || '',
      remember,
      firstOpen,
      loading: false,
      error: '',
      refreshToken: refreshToken || '',
    };
  },
  outboundState => {
    const {token, deviceToken, remember, firstOpen} = outboundState;
    return {token, deviceToken, remember, firstOpen};
  },
  {whitelist: ['auth']},
);

const persistConfig = {
  key: 'root',
  storage: AsyncStorage,
  transforms: [authTransform],
  blacklist: ['popUp', 'school', 'lesson', 'question', 'netInfo', 'offline'],
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

const store = configureStore({
  reducer: persistedReducer,
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      immutableCheck: false,
      serializableCheck: false,
    }).prepend(listenerMiddleware.middleware),
  enhancers: getDefaultEnhancers => {
    if (__DEV__) {
      return getDefaultEnhancers().concat(reactotron.createEnhancer());
    }
    return getDefaultEnhancers();
  },
});

export const persistor = persistStore(store);

injectStore(store, persistor);

export type RootState = ReturnType<typeof appReducer>;
export type AppDispatch = typeof store.dispatch;
export const useReduxDispatch = (): AppDispatch => useDispatch<AppDispatch>();
export const useTypedSelector: TypedUseSelectorHook<RootState> = useSelector;

export default store;
