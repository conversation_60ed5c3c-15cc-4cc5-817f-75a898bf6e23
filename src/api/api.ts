import axios, {AxiosInstance, AxiosRequestConfig, AxiosResponse} from 'axios';
import {API_BASE_URL} from '@env';
import {resetAndNavigate} from '../navigation/NavigationServices.ts';
import {APP_SCREEN} from '../navigation/screenType.ts';
import i18n from '../i18n';
import NotificationService from '../../common/firebase/NotificationService';
import {fetchRefreshToken} from '../redux/reducer/fetchData.ts';

let _store: any;
let _persistor: any;
let isRefreshing = false;
let failedQueue: Array<{resolve: Function; reject: Function}> = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({resolve, reject}) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });

  failedQueue = [];
};

export const injectStore = (store: any, persistor: any) => {
  _store = store;
  _persistor = persistor;
  // Reset refresh state when store is injected (app restart)
  isRefreshing = false;
  failedQueue = [];
};

const createAxiosInstance = (baseURL: string = API_BASE_URL): AxiosInstance => {
  const instance = axios.create({
    baseURL,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  instance.interceptors.request.use(
    (config: AxiosRequestConfig | any) => {
      const state = _store?.getState?.();
      const token = state?.auth?.token;
      config.headers['accept-language'] = i18n.language || 'en';
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    error => Promise.reject(error),
  );

  instance.interceptors.response.use(
    (response: AxiosResponse) => response,
    async error => {
      const originalRequest = error.config;
      const {status} = error.response || {};

      if (status === 401 && !originalRequest._retry && _store) {
        if (isRefreshing) {
          // If refresh is already in progress, queue this request
          return new Promise((resolve, reject) => {
            failedQueue.push({resolve, reject});
          }).then(token => {
            originalRequest.headers.Authorization = `Bearer ${token}`;
            return instance(originalRequest);
          }).catch(err => {
            return Promise.reject(err);
          });
        }

        originalRequest._retry = true;
        isRefreshing = true;

        const state = _store.getState();
        const refreshToken = state?.auth?.refreshToken;

        if (refreshToken) {
          try {
            // Add timeout for refresh token request
            const refreshPromise = _store.dispatch(fetchRefreshToken({
              client_id: 'elp-web',
              refresh_token: refreshToken,
            }));

            const timeoutPromise = new Promise((_, reject) => {
              setTimeout(() => reject(new Error('Refresh token timeout')), 10000);
            });

            const refreshResult = await Promise.race([refreshPromise, timeoutPromise]);

            if (refreshResult.type === 'fetchRefreshToken/auth/fulfilled') {
              const newTokens = refreshResult.payload;

              _store.dispatch({
                type: 'Auth/logIn',
                payload: {
                  accessToken: newTokens.access_token,
                  refreshToken: newTokens.refresh_token,
                },
              });

              // Process queued requests with new token
              processQueue(null, newTokens.access_token);
              isRefreshing = false;

              originalRequest.headers.Authorization = `Bearer ${newTokens.access_token}`;
              return instance(originalRequest);
            } else {
              processQueue(error, null);
              isRefreshing = false;
              NotificationService.clearNotificationState();
              _store.dispatch({type: 'RESET_STATE'});
              _persistor?.purge?.();
              resetAndNavigate(APP_SCREEN.AUTH);
              return Promise.reject(error);
            }
          } catch (refreshError) {
            processQueue(refreshError, null);
            isRefreshing = false;
            NotificationService.clearNotificationState();
            _store.dispatch({type: 'RESET_STATE'});
            _persistor?.purge?.();
            resetAndNavigate(APP_SCREEN.AUTH);
            return Promise.reject(refreshError);
          }
        } else {
          processQueue(error, null);
          isRefreshing = false;
          NotificationService.clearNotificationState();
          _store.dispatch({type: 'RESET_STATE'});
          _persistor?.purge?.();
          resetAndNavigate(APP_SCREEN.AUTH);
          return Promise.reject(error);
        }
      } else if (status === 403 && _store) {
        NotificationService.clearNotificationState();
        _store.dispatch({type: 'RESET_STATE'});
        _persistor?.purge?.();
        resetAndNavigate(APP_SCREEN.AUTH);
      }

      return Promise.reject(error);
    },
  );

  return instance;
};

const api = createAxiosInstance();

export {api, createAxiosInstance};
