import {useCallback, useRef, useState} from 'react';
import {resetAndNavigate} from '../../navigation/NavigationServices';
import {APP_SCREEN} from '../../navigation/screenType';
import {isValidEmail} from '../../utils';
import {OtpInputRef} from '../../components/form/OtpInput';
import {useReduxDispatch, useTypedSelector} from '../../redux/store';
import {
  fetchForgotPassword,
  fetchResetPassword,
  fetchVerifyOTP,
} from '../../redux/reducer/fetchData';
import Toast from 'react-native-toast-message';

type Status = 'first' | 'second' | 'third';

type FormDataType = {
  email: string;
  password: string;
  otp: string;
  confirmPassword: string;
};

const defaultFormData: FormDataType = {
  email: '',
  password: '',
  otp: '',
  confirmPassword: '',
};

export const useForgotPassword = () => {
  const dispatch = useReduxDispatch();
  const [status, setStatus] = useState<Status>('first');
  const [formData, setFormData] = useState<FormDataType>(defaultFormData);
  const [focused, setFocused] = useState({
    email: false,
    password: false,
    confirmPassword: false,
  });
  const [wrongOtpCount, setWrongOtpCount] = useState<number>(0);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const otpInputRef = useRef<OtpInputRef>(null);
  const loading = useTypedSelector(state => state.auth.loading);

  const rules = {
    validateLength:
      formData?.password?.length > 7
        ? true
        : formData?.password?.length > 0
          ? false
          : undefined,
    hasNumber: /\d/.test(formData?.password)
      ? true
      : formData?.password?.length > 0
        ? false
        : undefined,
    isMatch:
      formData?.confirmPassword?.length > 0
        ? formData?.password === formData?.confirmPassword
        : undefined,
  };

  const onChangeTextInput = useCallback(
    (key: keyof typeof formData, value: string) => {
      setFormData(prev => ({...prev, [key]: value}));
    },
    [],
  );

  const onFocusInput = (key: keyof typeof focused) => {
    setFocused(prev => ({...prev, [key]: true}));
  };

  const onBlurInput = (key: keyof typeof focused) => {
    setFocused(prev => ({...prev, [key]: true}));

    const value = formData[key]?.trim();
    if (!value) {
      const message =
        key === 'email'
          ? 'forgot.pleaseEnterEmail'
          : 'forgot.createNewPassword.pleaseEnterPassword';
      setErrors(prev => ({...prev, [key]: message}));
      return;
    }

    if (key === 'email' && !isValidEmail(value)) {
      setErrors(prev => ({...prev, [key]: 'forgot.invalidMail'}));
      return;
    }

    setErrors(prev => ({...prev, [key]: ''}));
  };

  const handleCreateNewPassword = async () => {
    const params = {
      email: formData?.email,
      otp: formData?.otp,
      newPassword: formData?.password,
    };
    const result = await dispatch(fetchResetPassword(params));
    if (result.type.endsWith('/fulfilled')) {
      const res = result.payload;
      if (res?.code === 0) {
        Toast.show({text1: `forgot.${res?.msgCode}`, type: 'customSuccess'});
        resetAndNavigate(APP_SCREEN.LOGIN);
        return;
      } else if (res?.code === 1) {
        Toast.show({text1: `forgot.${res?.msgCode}`, type: 'customError'});
      }
    }
  };

  const handleVerifyOTP = async (otp: string) => {
    const params = {
      email: formData?.email,
      otp: otp,
    };
    const result = await dispatch(fetchVerifyOTP(params));
    if (result.type.endsWith('/fulfilled')) {
      const res = result.payload;
      if (res?.code === 0) {
        setFormData(prev => ({...prev, otp: otp}));
        setStatus('third');
        setWrongOtpCount(0);
      } else if (res?.code === 1) {
        const newCount = wrongOtpCount + 1;
        setWrongOtpCount(newCount);
        otpInputRef?.current?.updateErorr(`forgot.${res?.msgCode}`);
        if (newCount >= 5) {
          setStatus('first');
          setWrongOtpCount(0);
        }
      }
    }
  };

  const handleSendOTPCode = async () => {
    const params = {
      email: formData?.email,
    };
    const result = await dispatch(fetchForgotPassword(params));
    if (result.type.endsWith('/fulfilled')) {
      const res = result.payload;
      if (res?.code === 0) {
        Toast.show({text1: `forgot.${res?.msgCode}`, type: 'customSuccess'});
        setStatus('second');
        return;
      } else if (res?.code === 1) {
        Toast.show({text1: `forgot.${res?.msgCode}`, type: 'customError'});
      }
    }
  };

  const handleReSendOTP = async () => {
    const params = {
      email: formData?.email,
    };
    const result = await dispatch(fetchForgotPassword(params));
    if (result.type.endsWith('/fulfilled')) {
      const res = result.payload;
      if (res?.code === 0) {
        Toast.show({text1: `forgot.${res?.msgCode}`, type: 'customSuccess'});
        setWrongOtpCount(0);
        otpInputRef?.current?.resendOTP();
        return;
      } else if (res?.code === 1) {
        Toast.show({text1: `forgot.${res?.msgCode}`, type: 'customError'});
      }
    }
  };

  return {
    otpInputRef,
    status,
    formData,
    focused,
    errors,
    handleVerifyOTP,
    onChangeTextInput,
    onFocusInput,
    onBlurInput,
    handleSendOTPCode,
    handleReSendOTP,
    handleCreateNewPassword,
    rules,
    loading,
  };
};
