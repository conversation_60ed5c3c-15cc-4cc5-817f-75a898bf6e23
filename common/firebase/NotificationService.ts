import notifee, {AndroidImportance, EventType} from '@notifee/react-native';
import messaging from '@react-native-firebase/messaging';
import {AppState} from 'react-native';
import {navigate} from '../../src/navigation/NavigationServices';
import {APP_SCREEN} from '../../src/navigation/screenType';
import store from '../../src/redux/store';
import {fetchCountMessage} from '../../src/redux/reducer/fetchData';

let pendingNavigation: {screen: string; params?: any} | null = null;
let lastNotificationId: string | null = null;
let appState: string = AppState.currentState;
let processedInitialNotifications: Set<string> = new Set();
let refreshCountTimeout: NodeJS.Timeout | null = null;

AppState.addEventListener('change', (nextAppState: string) => {
  if (appState.match(/inactive|background/) && nextAppState === 'active') {
    NotificationService.refreshNotificationCount();
    if (pendingNavigation) {
      NotificationService.checkPendingNavigation();
    }
  }
  appState = nextAppState;
});

const NotificationService = {
  initialize: async (): Promise<void> => {
    await NotificationService.requestPermission();
    await NotificationService.createDefaultChannel();
    NotificationService.setupNotificationListeners();
  },

  refreshNotificationCount: (): void => {
    if (refreshCountTimeout) {
      clearTimeout(refreshCountTimeout);
    }

    refreshCountTimeout = setTimeout(() => {
      const {token} = store.getState().auth;
      const {data: profileData} = store.getState().profile;

      const isLoggedIn =
        token &&
        profileData &&
        profileData.authorities &&
        profileData.authorities.length > 0;

      if (isLoggedIn) {
        store.dispatch(fetchCountMessage());
      }
      refreshCountTimeout = null;
    }, 500);
  },

  requestPermission: async (): Promise<void> => {
    await notifee.requestPermission();
  },

  createDefaultChannel: async (): Promise<void> => {
    await notifee.createChannel({
      id: 'default',
      name: 'Default Channel',
      importance: AndroidImportance.HIGH,
    });
  },

  getDeviceToken: async (): Promise<string | null> => {
    try {
      const token = await messaging().getToken();
      return token;
    } catch (error) {
      console.error('Error getting FCM token:', error);
      return null;
    }
  },

  setupNotificationListeners: (): void => {
    messaging().onMessage(async remoteMessage => {
      console.log('Foreground message received:', remoteMessage);
      await NotificationService.displayNotification(remoteMessage);
      NotificationService.refreshNotificationCount();
    });

    messaging().onNotificationOpenedApp(remoteMessage => {
      NotificationService.handleNotificationTap(remoteMessage);
    });

    notifee.onForegroundEvent(({type, detail}) => {
      if (type === EventType.PRESS) {
        NotificationService.handleNotificationPress(detail);
      }
    });
  },

  checkInitialNotification: async (): Promise<void> => {
    const remoteMessage = await messaging().getInitialNotification();
    if (remoteMessage) {
      const notificationId =
        remoteMessage.messageId ||
        remoteMessage.notification?.title +
          '_' +
          (remoteMessage.sentTime || Date.now());

      if (processedInitialNotifications.has(notificationId)) {
        return;
      }
      processedInitialNotifications.add(notificationId);
      NotificationService.handleNotificationTap(remoteMessage);
    }
  },

  getNavigationFromData: (data: any): {target: string; params?: any} => {
    if (!data) {
      return {target: APP_SCREEN.NOTIFICATION};
    }

    let parsedData = data;
    if (typeof data === 'string') {
      try {
        parsedData = JSON.parse(data);
      } catch (error) {
        console.error('Error parsing notification data:', error);
        return {target: APP_SCREEN.NOTIFICATION};
      }
    }

    switch (parsedData.code) {
      case 'STD_ADDED_TO_CLASS':
        return {
          target: APP_SCREEN.YOUR_CLASS,
          params: {
            classId: parsedData.classId,
          },
        };
      default:
        return {
          target: parsedData.target || APP_SCREEN.NOTIFICATION,
          params: parsedData,
        };
    }
  },

  displayNotification: async (remoteMessage: any): Promise<void> => {
    const messageId =
      remoteMessage.messageId ||
      remoteMessage.notification?.title + '_' + Date.now();

    if (messageId === lastNotificationId) {
      return;
    }
    lastNotificationId = messageId;

    setTimeout(() => {
      if (lastNotificationId === messageId) {
        lastNotificationId = null;
      }
    }, 5000);

    await notifee.displayNotification({
      id: messageId,
      title: remoteMessage?.notification?.title,
      body: remoteMessage?.notification?.body,
      android: {
        channelId: 'default',
        pressAction: {id: 'open_notification'},
        importance: AndroidImportance.HIGH,
      },
      data: {
        target: APP_SCREEN.NOTIFICATION,
        messageId: messageId,
        ...remoteMessage.data,
      },
    });
  },

  handleNotificationTap: (remoteMessage: any): void => {
    const {target, params} = NotificationService.getNavigationFromData(remoteMessage.data);
    NotificationService.navigateFromNotification(target, params);
    NotificationService.refreshNotificationCount();
  },

  handleNotificationPress: (detail: any): void => {
    const {target, params} = NotificationService.getNavigationFromData(detail.notification?.data);
    NotificationService.navigateFromNotification(target, params);
    NotificationService.refreshNotificationCount();
  },

  navigateFromNotification: (screen: string, data?: any): void => {
    setTimeout(() => {
      const {token} = store.getState().auth;
      const {data: profileData} = store.getState().profile;

      const isLoggedIn =
        token &&
        profileData &&
        profileData.authorities &&
        profileData.authorities.length > 0;

      if (!isLoggedIn) {
        pendingNavigation = {screen, params: data};
        return;
      }

      navigate(screen, data);
      if (pendingNavigation && pendingNavigation.screen === screen) {
        pendingNavigation = null;
      }
    }, 500);
  },

  checkPendingNavigation: async (): Promise<void> => {
    await NotificationService.checkInitialNotification();

    if (pendingNavigation) {
      const {token} = store.getState().auth;
      const {data: profileData} = store.getState().profile;
      const isLoggedIn =
        token &&
        profileData &&
        profileData.authorities &&
        profileData.authorities.length > 0;

      if (isLoggedIn) {
        navigate(pendingNavigation.screen, pendingNavigation.params);
        pendingNavigation = null;
      }
    }
  },

  clearNotificationState: (): void => {
    pendingNavigation = null;
    lastNotificationId = null;
    processedInitialNotifications.clear();
    if (refreshCountTimeout) {
      clearTimeout(refreshCountTimeout);
      refreshCountTimeout = null;
    }
  },

  handleBackgroundEvent: async ({
    type,
    detail,
  }: {
    type: EventType;
    detail: any;
  }): Promise<void> => {
    if (type === EventType.PRESS) {
      const {target, params} = NotificationService.getNavigationFromData(detail.notification?.data);
      pendingNavigation = {screen: target, params};
      NotificationService.refreshNotificationCount();
    }
  },
};

export default NotificationService;
